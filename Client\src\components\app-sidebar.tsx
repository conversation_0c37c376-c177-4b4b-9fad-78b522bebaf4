import * as React from "react";
import { useEffect, useState } from "react";
import {
  LogOut,
  Home,
  BarChart3,
  TrendingUp,
  Wallet,
  History,
  Bot,
  Settings2,
  X,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { config } from "@/lib/config";
import { NavMain } from "@/components/nav-main";
import { NavUser } from "@/components/nav-user";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  useSidebar,
} from "@/components/ui/sidebar";
import { auth } from "@/lib/firebase";
import { onAuthStateChanged } from "firebase/auth";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

// Navigation data for crypto trading dashboard
const data = {
  navMain: [
    {
      title: "Dashboard",
      url: "/dashboard",
      icon: Home,
      isActive: true,
    },
    {
      title: "Portfolio",
      url: "/portfolio",
      icon: BarChart3,
      items: [
        {
          title: "Overview",
          url: "/portfolio",
        },
        {
          title: "Holdings",
          url: "/portfolio/holdings",
        },
        {
          title: "Performance",
          url: "/portfolio/performance",
        },
      ],
    },
    {
      title: "Trading",
      url: "/trading",
      icon: TrendingUp,
      items: [
        {
          title: "Spot Trading",
          url: "/trading/spot",
        },
        {
          title: "Bot Trading",
          url: "/trading/bot",
        },
        {
          title: "Order History",
          url: "/trading/history",
        },
      ],
    },
    {
      title: "Wallet",
      url: "/wallet",
      icon: Wallet,
      items: [
        {
          title: "Balances",
          url: "/wallet/balances",
        },
        {
          title: "Transactions",
          url: "/wallet/transactions",
        },
        {
          title: "Deposit",
          url: "/wallet/deposit",
        },
        {
          title: "Withdraw",
          url: "/wallet/withdraw",
        },
      ],
    },
    {
      title: "Bot Management",
      url: "/bots",
      icon: Bot,
      items: [
        {
          title: "Active Bots",
          url: "/bots/active",
        },
        {
          title: "Create Bot",
          url: "/bots/create",
        },
        {
          title: "Strategies",
          url: "/bots/strategies",
        },
        {
          title: "Backtest",
          url: "/bots/backtest",
        },
      ],
    },
    {
      title: "History",
      url: "/history",
      icon: History,
      items: [
        {
          title: "Trade History",
          url: "/history/trades",
        },
        {
          title: "P&L History",
          url: "/history/pnl",
        },
        {
          title: "Bot Activity",
          url: "/history/bot-activity",
        },
      ],
    },
    {
      title: "Settings",
      url: "/settings",
      icon: Settings2,
      items: [
        {
          title: "Profile",
          url: "/settings/profile",
        },
        {
          title: "Security",
          url: "/settings/security",
        },
        {
          title: "API Keys",
          url: "/settings/api-keys",
        },
        {
          title: "Notifications",
          url: "/settings/notifications",
        },
        {
          title: "Preferences",
          url: "/settings/preferences",
        },
      ],
    },
  ],
};

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const navigate = useNavigate();
  const { logout, user: authUser } = useAuth();
  const [firebaseUser, setFirebaseUser] = useState(auth.currentUser);

  // Listen for Firebase auth state changes
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      setFirebaseUser(user);
    });

    return () => unsubscribe();
  }, []);

  // Determine which user data to use
  const userData =
    authUser ||
    (firebaseUser
      ? {
          name: firebaseUser.displayName || "User",
          email: firebaseUser.email || "<EMAIL>",
          avatar: firebaseUser.photoURL || "",
        }
      : null);

  const { isMobile, state, setOpenMobile } = useSidebar();

  return (
    <Sidebar
      collapsible={isMobile ? "offcanvas" : "icon"}
      className={isMobile ? "w-[320px]" : ""}
      {...props}
    >
      <SidebarHeader className={isMobile ? "p-0" : ""}>
        {isMobile && (
          <div className="flex items-center justify-between p-4 border-b bg-sidebar">
            <h2 className="text-xl font-bold text-sidebar-foreground">
              Crypto Pilot
            </h2>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setOpenMobile(false)}
              className="h-8 w-8 text-sidebar-foreground hover:bg-sidebar-accent"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        )}
        {!isMobile && <NavUser user={userData} />}
      </SidebarHeader>
      <SidebarContent className={isMobile ? "overflow-hidden" : ""}>
        {isMobile && (
          <div className="px-4 py-3 border-b bg-sidebar sticky top-0 z-10">
            <NavUser user={userData} />
          </div>
        )}
        <div className={isMobile ? "overflow-y-auto flex-1" : ""}>
          <NavMain items={data.navMain} />
          {/* <NavProjects projects={data.projects} /> */}
        </div>
      </SidebarContent>
      <SidebarFooter className={isMobile ? "hidden" : ""}>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="destructive"
                size="sm"
                onClick={async () => {
                  try {
                    // Clear avatar from localStorage and sessionStorage
                    localStorage.removeItem("userAvatar");
                    sessionStorage.removeItem("userAvatar");
                    localStorage.removeItem("avatarUrl");
                    sessionStorage.removeItem("avatarUrl");

                    // Clear any cookies that might store avatar data
                    document.cookie =
                      "userAvatar=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
                    document.cookie =
                      "avatarUrl=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";

                    // Force browser to clear image cache for this user
                    if (userData && userData.avatar) {
                      const img = new Image();
                      img.src =
                        userData.avatar + "?clear=" + new Date().getTime();
                    }

                    await fetch(`${config.api.baseUrl}/api/auth/logout`, {
                      method: "POST",
                      credentials: "include",
                    });
                    await logout();
                    navigate("/login");
                  } catch (error) {
                    console.error("Logout failed:", error);
                  }
                }}
                className="group-data-[state=collapsed]:w-8 group-data-[state=collapsed]:h-8 group-data-[state=collapsed]:p-0 group-data-[state=collapsed]:flex group-data-[state=collapsed]:justify-center group-data-[state=collapsed]:items-center group-data-[state=collapsed]:mx-auto group-data-[state=collapsed]:ml-[calc(50%-20px)] transition-all"
              >
                <LogOut className="h-4 w-4 mr-2 group-data-[state=collapsed]:mr-0" />
                <span className="group-data-[state=collapsed]:hidden">
                  Logout
                </span>
              </Button>
            </TooltipTrigger>
            <TooltipContent
              side="right"
              align="center"
              hidden={state !== "collapsed"}
            >
              Logout
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </SidebarFooter>
    </Sidebar>
  );
}
