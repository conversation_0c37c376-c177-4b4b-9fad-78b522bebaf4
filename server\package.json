{"name": "server", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "jest", "build": "npm audit fix && npm install && mkdir -p public"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"bcryptjs": "^2.4.3", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "crypto": "^1.0.1", "csurf": "^1.2.2", "dotenv": "^16.4.7", "envalid": "^8.0.0", "express": "^4.21.2", "express-rate-limit": "^7.5.0", "express-validator": "^7.2.1", "firebase-admin": "^11.10.1", "helmet": "^8.0.0", "jsonwebtoken": "^9.0.2", "mongodb": "^6.12.0", "mongoose": "^8.9.3", "react-router-dom": "^7.3.0"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.1.9", "supertest": "^7.0.0"}}