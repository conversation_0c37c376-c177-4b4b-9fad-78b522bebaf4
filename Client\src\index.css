@import "tailwindcss";

@plugin "tailwindcss-animate";

@custom-variant dark (&:is(.dark *));

:root {
  font-family: "futura", system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  --background: oklch(1 0 0);
  --foreground: oklch(0.129 0.042 264.695);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.129 0.042 264.695);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.129 0.042 264.695);
  --primary: oklch(0.208 0.042 265.755);
  --primary-foreground: oklch(0.984 0.003 247.858);
  --secondary: oklch(0.968 0.007 247.896);
  --secondary-foreground: oklch(0.208 0.042 265.755);
  --muted: oklch(0.968 0.007 247.896);
  --muted-foreground: oklch(0.554 0.046 257.417);
  --accent: oklch(0.968 0.007 247.896);
  --accent-foreground: oklch(0.208 0.042 265.755);
  --destructive: oklch(0.577 0.245 27.325);
  --destructive-foreground: oklch(0.577 0.245 27.325);
  --border: oklch(0.929 0.013 255.508);
  --input: oklch(0.929 0.013 255.508);
  --ring: oklch(0.704 0.04 256.788);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --radius: 0.625rem;
  --sidebar: oklch(0.984 0.003 247.858);
  --sidebar-foreground: oklch(0.129 0.042 264.695);
  --sidebar-primary: oklch(0.208 0.042 265.755);
  --sidebar-primary-foreground: oklch(0.984 0.003 247.858);
  --sidebar-accent: oklch(0.968 0.007 247.896);
  --sidebar-accent-foreground: oklch(0.208 0.042 265.755);
  --sidebar-border: oklch(0.929 0.013 255.508);
  --sidebar-ring: oklch(0.704 0.04 256.788);
}

/* Add responsive container styles */
.crypto-dashboard-title {
  font-family: "Alien Moon", sans-serif;
  font-weight: bold;
  letter-spacing: 0.5px;
  font-size: clamp(1.2rem, 4vw, 2rem); /* Responsive font size */
  text-align: center;
  padding: 0 1rem;
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}
a:hover {
  color: #535bf2;
}

.recharts-curve {
  transition: stroke-dashoffset 1.5s ease-out;
}

body {
  margin: 0;
  place-items: center;
  min-width: 320px;
  min-height: 100vh;
  overflow-x: hidden;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

@font-face {
  font-family: "Alien Moon";
  src: url("/fonts/alien-moon/AlienMoon-3zrYX.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Alien Moon";
  src: url("/fonts/alien-moon/AlienMoonItalic-OVPlO.ttf") format("truetype");
  font-weight: normal;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: "Alien Moon";
  src: url("/fonts/alien-moon/AlienMoonItalic-OVPlO.ttf") format("truetype");
  font-weight: normal;
  font-style: italic;
  font-display: swap;
}

/* Add Nuixyber font declarations */
@font-face {
  font-family: "Nuixyber";
  src: url("/fonts/nuixyber/NuixyberInnoConvert-9M9vj.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Nuixyber";
  src: url("/fonts/nuixyber/NuixyberInnoConvertItalic-lx5pD.ttf")
    format("truetype");
  font-weight: normal;
  font-style: italic;
  font-display: swap;
}

@import "tailwindcss";

.no-scrollbar {
  overflow-y: auto;
  scrollbar-width: none;
  /* Firefox */
  -ms-overflow-style: none;
  /* IE 10+ */
}

.no-scrollbar::-webkit-scrollbar {
  display: none;
  /* Chrome, Safari, Opera */
}

/* Mobile optimization utilities */
.no-scrollbar::-webkit-scrollbar {
  display: none;
}

.no-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

/* Enhanced Mobile Responsiveness */
@media (max-width: 768px) {
  :root {
    --card-padding: 0.5rem;
    --sidebar-width: 280px;
  }

  /* Improve container layout */
  body {
    overflow-x: hidden;
    max-width: 100vw;
    padding: 0;
    font-size: 14px;
  }

  /* Make all containers respect viewport width */
  div,
  section,
  main {
    max-width: 100%;
    width: 100%;
    box-sizing: border-box;
  }

  /* Adjust card layouts for mobile */
  .card {
    margin: 0.25rem 0;
    width: 100%;
    min-width: unset;
    border-radius: 8px;
  }

  /* Mobile-friendly button sizing */
  button:not([data-size="icon"]) {
    min-height: 44px;
    padding: 0.75rem 1rem;
    font-size: 14px;
  }

  /* Icon buttons for mobile */
  button[data-size="icon"] {
    min-height: 40px;
    min-width: 40px;
    padding: 0.5rem;
  }

  /* Input fields for mobile */
  input,
  select,
  textarea {
    min-height: 44px;
    font-size: 16px; /* Prevents zoom on iOS */
    padding: 0.75rem;
  }

  /* Charts and data visualizations */
  .recharts-wrapper,
  .chart-container {
    width: 100% !important;
    max-width: 100%;
    overflow-x: hidden;
    height: 200px !important;
  }

  /* Mobile grid adjustments */
  .grid {
    gap: 0.75rem !important;
  }

  /* Typography adjustments */
  h1 {
    font-size: 1.5rem !important;
    line-height: 1.2;
  }

  h2 {
    font-size: 1.25rem !important;
  }

  h3 {
    font-size: 1.125rem !important;
  }

  /* Table responsiveness */
  .table-container {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  table {
    min-width: 100%;
    font-size: 12px;
  }

  th,
  td {
    padding: 0.5rem 0.25rem;
    white-space: nowrap;
  }
}

/* Touch-friendly interactions */
@media (max-width: 768px) {
  /* Improve touch targets */
  button,
  [role="button"],
  a,
  input[type="checkbox"],
  input[type="radio"] {
    min-height: 44px;
    min-width: 44px;
  }

  /* Smooth scrolling for mobile */
  * {
    -webkit-overflow-scrolling: touch;
  }

  /* Prevent text selection on UI elements */
  button,
  [role="button"] {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  /* Improve tap highlighting */
  button,
  [role="button"],
  a {
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
  }
}

/* Extra small screen optimizations */
@media (max-width: 380px) {
  :root {
    --card-padding: 0.375rem;
  }

  body {
    padding: 0;
    font-size: 13px;
  }

  /* Further reduce spacing */
  .card {
    margin: 0.125rem 0;
    border-radius: 6px;
  }

  /* Smaller buttons for very small screens */
  button:not([data-size="icon"]) {
    min-height: 40px;
    padding: 0.5rem 0.75rem;
    font-size: 13px;
  }

  /* Compact grid */
  .grid {
    gap: 0.5rem !important;
  }

  /* Smaller typography */
  h1 {
    font-size: 1.25rem !important;
  }

  h2 {
    font-size: 1.125rem !important;
  }

  h3 {
    font-size: 1rem !important;
  }
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #1a1a1a;
  color: white; /* Explicitly set text color for dark backgrounds */
  cursor: pointer;
  transition: border-color 0.25s, background-color 0.25s, color 0.25s;
}
button:hover {
  border-color: #ffffff;
}
button:focus,
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
}

@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }
  a:hover {
    color: #747bff;
  }
  button {
    background-color: #f9f9f9;
    color: #213547; /* Dark text on light background */
  }
  /* Style for social login buttons in light mode */
  button.social-login {
    color: #213547; /* Ensure dark text on light background */
  }
}

/* Add specific dark mode button styles */
.dark button {
  background-color: var(--primary);
  color: var(--primary-foreground); /* Ensure proper contrast */
}

/* Social login button styles for dark mode */
.dark button.social-login {
  background-color: var(--secondary);
  color: var(--secondary-foreground); /* Ensure contrast */
}

.dark {
  --background: oklch(0.129 0.042 264.695);
  --foreground: oklch(0.984 0.003 247.858);
  --card: oklch(0.129 0.042 264.695);
  --card-foreground: oklch(0.984 0.003 247.858);
  --popover: oklch(0.129 0.042 264.695);
  --popover-foreground: oklch(0.984 0.003 247.858);
  --primary: oklch(0.984 0.003 247.858);
  --primary-foreground: oklch(0.208 0.042 265.755);
  --secondary: oklch(0.279 0.041 260.031);
  --secondary-foreground: oklch(0.984 0.003 247.858);
  --muted: oklch(0.279 0.041 260.031);
  --muted-foreground: oklch(0.704 0.04 256.788);
  --accent: oklch(0.279 0.041 260.031);
  --accent-foreground: oklch(0.984 0.003 247.858);
  --destructive: oklch(0.396 0.141 25.723);
  --destructive-foreground: oklch(
    0.837 0.237 25.331
  ); /* Made brighter for dark mode */
  --border: oklch(0.279 0.041 260.031);
  --input: oklch(0.279 0.041 260.031);
  --ring: oklch(0.446 0.043 257.281);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.208 0.042 265.755);
  --sidebar-foreground: oklch(0.984 0.003 247.858);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.984 0.003 247.858);
  --sidebar-accent: oklch(0.279 0.041 260.031);
  --sidebar-accent-foreground: oklch(0.984 0.003 247.858);
  --sidebar-border: oklch(0.279 0.041 260.031);
  --sidebar-ring: oklch(0.446 0.043 257.281);
}

/* Add specific styling for the red values in dark mode */
.dark .text-red-400 {
  color: rgb(248 113 113); /* Brighter red that's more visible in dark mode */
}

.dark .bg-red-500\/20 {
  background-color: rgb(239 68 68 / 0.2); /* Slightly more visible background */
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Add responsive container styles */
@media (max-width: 768px) {
  /* Adjust button and toggle sizes for mobile */
  button[size="icon"],
  button[data-size="icon"] {
    height: 32px;
    width: 32px;
    padding: 0;
  }

  /* Adjust theme toggle specifically */
  button[aria-label="Toggle theme"],
  .mode-toggle button {
    height: 28px;
    width: 28px;
    padding: 4px;
  }

  /* Adjust switch/toggle components */
  [data-slot="switch"] {
    height: 16px !important;
    width: 28px !important;
  }

  [data-slot="switch-thumb"] {
    width: 12px !important;
    height: 12px !important;
  }

  /* Adjust icon sizes within buttons */
  button svg,
  [role="button"] svg {
    width: 16px;
    height: 16px;
  }

  /* Adjust dropdown menu items */
  .dropdown-menu-content {
    font-size: 0.875rem;
    padding: 0.25rem;
  }

  /* Make regular buttons more compact */
  button:not([size="icon"]) {
    padding: 0.4em 0.8em;
    font-size: 0.875em;
    min-height: 32px;
  }
}

/* Extra small screen adjustments */
@media (max-width: 380px) {
  button[size="icon"],
  button[data-size="icon"] {
    height: 28px;
    width: 28px;
  }

  [data-slot="switch"] {
    height: 14px !important;
    width: 24px !important;
  }

  [data-slot="switch-thumb"] {
    width: 10px !important;
    height: 10px !important;
  }
}

/* Mobile-specific adjustments */
@media (max-width: 768px) {
  /* Reset and fix Select trigger */
  .SelectTrigger,
  [data-slot="select-trigger"] {
    width: 100% !important;
    min-width: unset !important;
    max-width: 100% !important;
  }

  /* Fix portal and content positioning */
  .SelectPortal,
  [data-radix-select-portal] {
    position: fixed !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    width: calc(100% - 32px) !important;
    max-width: 300px !important;
  }

  /* Fix content container */
  [data-slot="select-content"] {
    position: relative !important;
    width: 100% !important;
    min-width: unset !important;
    max-width: none !important;
    margin: 4px 0 !important;
    transform: none !important;
    left: 0 !important;
    height: auto !important;
    min-height: 200px !important;
  }

  /* Fix viewport */
  [data-slot="select-content"] .SelectViewport {
    width: 100% !important;
    padding: 8px !important;
    height: auto !important;
    min-height: 200px !important;
    max-height: 50vh !important;
  }

  /* Fix items alignment and visibility */
  [data-slot="select-item"] {
    width: 100% !important;
    padding: 12px !important;
    font-size: 16px !important;
    height: auto !important;
    min-height: 44px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: flex-start !important;
    position: relative !important;
    left: 0 !important;
    transform: none !important;
  }

  /* Fix item text alignment */
  [data-slot="select-item"] [data-slot="select-item-text"] {
    position: relative !important;
    left: 0 !important;
    transform: none !important;
    text-align: left !important;
    width: 100% !important;
  }

  /* Fix item indicator (checkmark) position */
  [data-slot="select-item"] [data-slot="select-item-indicator"] {
    position: absolute !important;
    right: 8px !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
  }

  /* Ensure proper z-index and visibility */
  [data-radix-select-portal] {
    z-index: 9999 !important;
    height: auto !important;
    min-height: 200px !important;
  }
}

/* Remove any interfering styles */
[data-slot="select-item"],
[data-slot="select-item"] * {
  position: relative !important;
  left: auto !important;
  right: auto !important;
}

/* Ensure text is visible and aligned properly */
[data-slot="select-item-text"] {
  display: block !important;
  width: calc(100% - 24px) !important; /* Account for checkmark */
  text-align: left !important;
  white-space: normal !important;
  overflow: visible !important;
}

/* Fix animation while maintaining height */
[data-slot="select-content"][data-side="bottom"] {
  animation: selectDropDown 0.2s ease-out !important;
  height: auto !important;
  min-height: 200px !important;
}

@keyframes selectDropDown {
  from {
    opacity: 0;
    transform: translateY(-4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Mobile-specific adjustments */
@media (max-width: 768px) {
  /* Fix Select component container */
  .SelectTrigger,
  [data-slot="select-trigger"] {
    width: 100% !important;
    min-width: 200px !important;
    max-width: 100% !important;
  }

  /* Fix dropdown content positioning and width */
  [data-slot="select-content"] {
    min-width: 200px !important;
    width: var(--radix-select-trigger-width) !important;
    max-width: calc(100vw - 24px) !important;
    position: relative !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
  }

  /* Fix Select viewport */
  .SelectViewport,
  [role="presentation"] .SelectViewport {
    width: 100% !important;
    padding: 4px !important;
    max-height: 300px !important;
  }

  /* Fix Select items */
  [data-slot="select-item"] {
    width: 100% !important;
    padding: 10px 12px !important;
    font-size: 14px !important;
    min-height: 40px !important;
  }

  /* Ensure proper positioning relative to trigger */
  [data-slot="select-content"][data-side="bottom"] {
    margin-top: 4px !important;
    top: 100% !important;
  }

  /* Fix Select value display */
  [data-slot="select-value"] {
    width: 100% !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: nowrap !important;
  }

  /* Bot Control Panel Fixes */
  .relative.inline-flex[data-state] {
    width: 42px !important;
    height: 22px !important;
    border-radius: 9999px !important;
    padding: 2px !important;
    background-color: var(--input) !important;
  }

  /* Toggle thumb (circle) */
  .relative.inline-flex[data-state] span {
    width: 18px !important;
    height: 18px !important;
    transform: translateX(0) !important;
  }

  /* Toggle states */
  .relative.inline-flex[data-state="checked"] {
    background-color: var(--primary) !important;
  }

  .relative.inline-flex[data-state="checked"] span {
    transform: translateX(20px) !important;
  }

  .relative.inline-flex[data-state="unchecked"] span {
    transform: translateX(0) !important;
  }

  /* Progress bars in bot panel */
  div[role="progressbar"] {
    width: 100% !important;
    height: 6px !important;
    border-radius: 3px !important;
  }

  /* Bot panel metrics spacing */
  .space-y-2 > div,
  .space-y-3 > div {
    margin: 0.5rem 0;
  }

  /* Keep toggle containers properly aligned */
  .flex.items-center.justify-between {
    display: flex !important;
    flex-direction: row !important;
    align-items: center !important;
    justify-content: space-between !important;
    width: 100% !important;
    margin: 0.5rem 0 !important;
  }

  /* Make dashboard title bigger */
  .crypto-dashboard-title {
    font-size: clamp(1.8rem, 6vw, 2.5rem);
    margin-bottom: 1rem;
  }

  /* Position mode toggle next to refresh button */
  .mode-toggle {
    position: static !important; /* Override any fixed positioning */
    margin-left: 0.5rem;
  }

  /* Adjust bot control toggles to be compact and properly shaped */
  div[role="switch"],
  .relative[data-state] {
    width: 36px !important;
    height: 20px !important;
    position: relative;
    flex-shrink: 0;
    margin-left: auto;
  }

  /* Adjust the toggle thumb (the circle part) */
  div[role="switch"] span,
  .relative[data-state] span {
    width: 16px !important;
    height: 16px !important;
    transform: translateY(-50%) !important;
    top: 50% !important;
  }

  /* Adjust the positioning of the thumb for checked/unchecked states */
  span[data-state="checked"] {
    transform: translateX(16px) translateY(-50%) !important;
  }

  span[data-state="unchecked"] {
    transform: translateX(2px) translateY(-50%) !important;
  }

  /* Fix dropdown and button sizes */
  .SelectTrigger,
  select[class*="SelectTrigger"],
  div[class*="SelectTrigger"] {
    width: auto !important;
    min-width: 120px;
    max-width: fit-content;
  }

  /* Ensure controls container stays horizontal */
  .flex.items-center.gap-2 {
    flex-direction: row !important;
    flex-wrap: nowrap;
    align-items: center;
  }

  /* Portfolio value timeframe dropdown */
  div[class*="portfolio-timeframe"] .SelectTrigger,
  div[class*="portfolio-timeframe"] select {
    width: auto !important;
    min-width: 90px;
    max-width: fit-content;
  }

  /* Fix button layout in flex containers */
  .flex button:not([size="icon"]) {
    width: auto;
  }

  /* Adjust control groups to prevent stretching */
  .flex-row,
  .items-center {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
    gap: 0.5rem;
  }

  /* Ensure controls don't stretch in their containers */
  .card-content .flex,
  .card-header .flex {
    align-items: center;
    justify-content: space-between;
    flex-direction: row;
  }
}

/* Mobile-specific adjustments */
@media (max-width: 768px) {
  /* Portal positioning */
  .SelectPortal,
  [data-radix-select-portal] {
    position: fixed !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    width: calc(100% - 32px) !important;
    max-width: 300px !important;
  }

  /* Content container */
  [data-slot="select-content"] {
    width: 100% !important;
    min-height: 200px !important;
  }

  /* Fix viewport */
  .SelectViewport {
    padding: 8px !important;
  }
}

/* Override Radix UI's default styles */
[data-slot="select-content"] {
  width: 100% !important;
  min-width: 200px !important;
  background: var(--popover) !important;
  border-radius: 6px !important;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;
}

/* Reset and fix item styles */
[data-slot="select-item"] {
  all: unset !important;
  display: flex !important;
  width: 100% !important;
  min-height: 40px !important;
  padding: 8px 12px !important;
  position: relative !important;
  cursor: pointer !important;
  user-select: none !important;
}

/* Fix text positioning */
[data-slot="select-item"] [data-radix-select-item-text] {
  all: unset !important;
  display: block !important;
  flex: 1 !important;
  text-align: left !important;
  padding-right: 24px !important; /* Space for checkmark */
}

/* Fix checkmark positioning */
[data-slot="select-item"] [data-radix-select-item-indicator] {
  position: absolute !important;
  right: 8px !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
}

/* Mobile adjustments */
@media (max-width: 768px) {
  [data-radix-select-portal] {
    position: fixed !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    width: calc(100% - 32px) !important;
    max-width: 300px !important;
  }

  [data-slot="select-content"] {
    width: 100% !important;
  }

  [data-slot="select-viewport"] {
    padding: 4px !important;
  }
}

/* Remove any transforms on text */
[data-radix-select-item-text] {
  transform: none !important;
  position: static !important;
  left: auto !important;
  right: auto !important;
}

/* Ensure proper text wrapping */
[data-slot="select-item"] span {
  white-space: normal !important;
  overflow: visible !important;
  text-overflow: clip !important;
}

/* Switch Component Styles */
[data-slot="switch"] {
  position: relative;
  display: inline-flex;
  height: 24px;
  width: 44px;
  flex-shrink: 0;
  cursor: pointer;
  border-radius: 9999px;
  border: 2px solid transparent;
  background-color: var(--input);
  transition: background-color 200ms;
}

[data-slot="switch"][data-state="checked"] {
  background-color: var(--primary);
}

[data-slot="switch-thumb"] {
  display: block;
  width: 20px;
  height: 20px;
  border-radius: 9999px;
  background-color: var(--background);
  box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  transition: transform 200ms;
  transform: translateX(2px);
  position: absolute;
  top: 0;
  left: 0;
}

[data-slot="switch"][data-state="checked"] [data-slot="switch-thumb"] {
  transform: translateX(20px);
}

/* Mobile-only switch styles */
@media (max-width: 768px) {
  /* Reset any existing switch styles */
  [data-slot="switch"],
  div[role="switch"],
  .relative.inline-flex[data-state] {
    all: unset;
    position: relative;
    display: inline-block;
    width: 32px;
    height: 18px;
    background-color: var(--input);
    border-radius: 10px;
    cursor: pointer;
    transition: background-color 200ms ease;
  }

  /* Thumb (circle) styles */
  [data-slot="switch-thumb"],
  div[role="switch"] span,
  .relative.inline-flex[data-state] span {
    position: absolute;
    top: 2px;
    left: 2px;
    width: 14px;
    height: 14px;
    background-color: var(--background);
    border-radius: 50%;
    transition: transform 200ms ease;
  }

  /* Checked state */
  [data-slot="switch"][data-state="checked"],
  div[role="switch"][data-state="checked"],
  .relative.inline-flex[data-state="checked"] {
    background-color: var(--primary);
  }

  /* Thumb position when checked */
  [data-slot="switch"][data-state="checked"] [data-slot="switch-thumb"],
  div[role="switch"][data-state="checked"] span,
  .relative.inline-flex[data-state="checked"] span {
    transform: translateX(14px);
  }

  /* Unchecked state thumb position */
  [data-slot="switch"][data-state="unchecked"] [data-slot="switch-thumb"],
  div[role="switch"][data-state="unchecked"] span,
  .relative.inline-flex[data-state="unchecked"] span {
    transform: translateX(0);
  }
}

/* Extra small screens */
@media (max-width: 380px) {
  [data-slot="switch"],
  div[role="switch"],
  .relative.inline-flex[data-state] {
    width: 28px;
    height: 16px;
  }

  [data-slot="switch-thumb"],
  div[role="switch"] span,
  .relative.inline-flex[data-state] span {
    width: 12px;
    height: 12px;
  }

  [data-slot="switch"][data-state="checked"] [data-slot="switch-thumb"],
  div[role="switch"][data-state="checked"] span,
  .relative.inline-flex[data-state="checked"] span {
    transform: translateX(12px);
  }
}

/* Mobile-specific Bot Control Panel styles */
@media (max-width: 768px) {
  /* Bot Control Panel Container */
  .bot-control-panel {
    padding: 12px;
  }

  /* Toggle Container */
  .bot-toggle-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;
    gap: 8px;
  }

  /* Toggle Switch Base */
  .bot-toggle {
    position: relative;
    width: 42px;
    height: 22px;
    border-radius: 11px;
    background-color: var(--input);
    cursor: pointer;
    transition: background-color 200ms;
    padding: 2px;
  }

  /* Toggle Switch Active State */
  .bot-toggle[data-state="checked"] {
    background-color: var(--primary);
  }

  /* Toggle Switch Thumb */
  .bot-toggle-thumb {
    position: absolute;
    width: 18px;
    height: 18px;
    background-color: var(--background);
    border-radius: 50%;
    transition: transform 200ms;
  }

  /* Toggle Switch Thumb Positions */
  .bot-toggle[data-state="checked"] .bot-toggle-thumb {
    transform: translateX(20px);
  }

  .bot-toggle[data-state="unchecked"] .bot-toggle-thumb {
    transform: translateX(0);
  }

  /* Bot Control Labels */
  .bot-control-label {
    font-size: 14px;
    font-weight: 500;
    color: var(--foreground);
  }

  /* Progress bars in bot panel */
  .bot-progress-bar {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    margin: 8px 0;
  }

  /* Bot metrics container */
  .bot-metrics {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin: 12px 0;
  }

  /* Bot metric item */
  .bot-metric-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
  }

  /* Bot control buttons */
  .bot-control-button {
    width: 100%;
    height: 36px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    margin-top: 12px;
  }

  /* Bot status indicators */
  .bot-status {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 4px;
    background-color: var(--accent);
  }

  /* DCA Settings section */
  .dca-settings {
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid var(--border);
  }

  /* Settings groups */
  .settings-group {
    margin-bottom: 16px;
  }

  /* Settings row */
  .settings-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
  }

  /* Value displays */
  .value-display {
    font-size: 14px;
    color: var(--muted-foreground);
  }
}

/* Extra small screens adjustments */
@media (max-width: 380px) {
  .bot-toggle {
    width: 36px;
    height: 20px;
  }

  .bot-toggle-thumb {
    width: 16px;
    height: 16px;
  }

  .bot-toggle[data-state="checked"] .bot-toggle-thumb {
    transform: translateX(16px);
  }

  .bot-control-button {
    height: 32px;
    font-size: 13px;
  }

  .bot-control-label {
    font-size: 13px;
  }
}

/* Bot Control Panel - Mobile First Design */
.bot-panel {
  background-color: var(--card);
  border: 1px solid var(--border);
}

/* Bot Metrics Section */
.bot-metrics {
  padding: 16px 0;
  border-bottom: 1px solid var(--border);
}

.metric-item {
  margin-bottom: 12px;
}

.metric-item:last-child {
  margin-bottom: 0;
}

.metric-label {
  font-size: 13px;
  margin-bottom: 6px;
  display: block;
}

.bot-progress {
  height: 6px;
  margin: 6px 0;
  background-color: var(--input);
  border-radius: 3px;
}

.metric-value {
  font-size: 12px;
  color: var(--muted-foreground);
}

/* Strategy Settings Section */
.strategy-settings {
  padding: 16px 0;
  border-bottom: 1px solid var(--border);
}

.settings-header {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 12px;
  display: block;
}

.settings-grid {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Bot Control Elements */
.bot-control-label {
  font-size: 13px;
  font-weight: 500;
}

/* Action Buttons */
.bot-actions {
  padding-top: 16px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.bot-control-button {
  width: 100%;
  height: 36px;
  font-size: 14px;
  font-weight: 500;
}

/* Extra Small Screen Adjustments */
@media (max-width: 380px) {
  .bot-control-label {
    font-size: 12px;
  }

  .bot-control-button {
    height: 32px;
    font-size: 13px;
  }

  .settings-header {
    font-size: 13px;
  }

  .metric-label {
    font-size: 12px;
  }
}

/* Tablet and Desktop Adjustments */
@media (min-width: 768px) {
  .settings-grid {
    flex-direction: row;
    flex-wrap: wrap;
    gap: 16px;
  }

  .setting-item {
    flex: 1 1 calc(50% - 8px);
  }

  .bot-actions {
    flex-direction: row;
  }

  .bot-control-button {
    flex: 1;
  }
}

/* Mode toggle responsive positioning */
.mode-toggle-container {
  position: fixed;
  right: 1rem;
  top: 1rem;
  z-index: 50;
  display: flex;
  justify-content: flex-end;
}

/* Mobile-specific layout adjustments */
@media (max-width: 768px) {
  .mode-toggle-container {
    right: 0.75rem;
    top: 0.75rem;
  }

  .mode-toggle {
    height: 2rem !important;
    width: 2rem !important;
    padding: 0.375rem !important;
  }

  /* Ensure main content doesn't overlap with bottom nav */
  main {
    padding-bottom: 5rem;
  }

  /* Mobile sidebar adjustments */
  [data-sidebar="sidebar"] {
    z-index: 50 !important;
    width: 320px !important;
    max-width: 85vw !important;
    overflow-x: hidden !important;
  }

  [data-sidebar="inset"] {
    width: 100% !important;
  }

  /* Force full width for all sidebar elements */
  [data-sidebar="sidebar"] *,
  [data-sidebar="sidebar"] div,
  [data-sidebar="sidebar"] nav,
  [data-sidebar="sidebar"] ul,
  [data-sidebar="sidebar"] li {
    width: 100% !important;
    max-width: 100% !important;
    overflow-x: hidden !important;
    box-sizing: border-box !important;
  }

  /* Fix sidebar buttons specifically */
  [data-sidebar="sidebar"] button {
    width: 100% !important;
    max-width: 100% !important;
    justify-content: flex-start !important;
    text-align: left !important;
    overflow: hidden !important;
    white-space: nowrap !important;
    box-sizing: border-box !important;
    padding: 0.5rem 1rem !important;
    margin: 0 !important;
    border: none !important;
    background: transparent !important;
  }

  /* Fix sidebar links */
  [data-sidebar="sidebar"] a {
    width: 100% !important;
    max-width: 100% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: flex-start !important;
    text-decoration: none !important;
    overflow: hidden !important;
    box-sizing: border-box !important;
  }

  /* Remove any transforms or positioning that might cause issues */
  [data-sidebar="sidebar"] * {
    transform: none !important;
    position: relative !important;
    left: auto !important;
    right: auto !important;
    top: auto !important;
    bottom: auto !important;
  }

  /* Mobile-friendly card spacing */
  .card-header {
    padding: 0.75rem !important;
  }

  .card-content {
    padding: 0.75rem !important;
  }

  /* Responsive text sizing */
  .text-responsive {
    font-size: clamp(0.875rem, 2.5vw, 1rem);
  }

  /* Mobile-optimized form elements */
  .form-item {
    margin-bottom: 1rem;
  }

  .form-label {
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
  }

  /* Improve dropdown positioning on mobile */
  .dropdown-content {
    max-height: 60vh;
    overflow-y: auto;
  }

  /* Mobile sidebar styling */
  [data-sidebar="sidebar"] {
    z-index: 50 !important;
  }

  /* Ensure sidebar buttons are properly sized on mobile */
  [data-sidebar="sidebar"] button,
  [data-sidebar="sidebar"] a {
    min-height: 48px !important;
    padding: 12px 16px !important;
    font-size: 16px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: flex-start !important;
    width: 100% !important;
    text-align: left !important;
    border-radius: 8px !important;
    margin: 2px 8px !important;
    transition: all 0.2s ease !important;
  }

  /* Sidebar navigation items spacing */
  [data-sidebar="sidebar"] [data-slot="sidebar-menu"] {
    padding: 8px !important;
  }

  [data-sidebar="sidebar"] [data-slot="sidebar-menu-item"] {
    margin-bottom: 4px !important;
  }

  /* Sidebar icons */
  [data-sidebar="sidebar"] svg {
    flex-shrink: 0 !important;
    margin-right: 12px !important;
    width: 20px !important;
    height: 20px !important;
  }

  /* Sidebar trigger visibility */
  [data-sidebar="trigger"] {
    display: flex !important;
  }

  /* Force reset all sidebar positioning and sizing */
  [data-sidebar="sidebar"] {
    position: fixed !important;
    left: 0 !important;
    top: 0 !important;
    height: 100vh !important;
    width: 280px !important;
    max-width: 280px !important;
    overflow-y: auto !important;
    overflow-x: hidden !important;
    z-index: 50 !important;
  }

  /* Reset all child elements */
  [data-sidebar="sidebar"] > * {
    width: 100% !important;
    max-width: 100% !important;
    overflow-x: hidden !important;
  }

  /* Specific fixes for menu items */
  [data-sidebar="sidebar"] [role="menuitem"],
  [data-sidebar="sidebar"] [role="button"],
  [data-sidebar="sidebar"] .group\\/collapsible {
    width: 100% !important;
    max-width: 100% !important;
    overflow: hidden !important;
  }

  /* Force navigation links to be full width */
  [data-sidebar="sidebar"] a {
    width: 100% !important;
    max-width: 100% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: flex-start !important;
    padding: 0.5rem 0.75rem !important;
    text-decoration: none !important;
    border-radius: 0.375rem !important;
    margin: 0.125rem 0 !important;
  }

  /* Navigation icons and text */
  [data-sidebar="sidebar"] a svg {
    flex-shrink: 0 !important;
    margin-right: 0.5rem !important;
  }

  [data-sidebar="sidebar"] a span {
    flex: 1 !important;
    text-align: left !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: nowrap !important;
  }

  /* Force text to be visible on mobile - override collapsed state */
  [data-sidebar="sidebar"] span {
    display: inline-block !important;
    opacity: 1 !important;
    visibility: visible !important;
    width: auto !important;
    max-width: none !important;
    font-size: 14px !important;
    color: inherit !important;
  }

  /* Force sidebar to be expanded on mobile */
  [data-sidebar="sidebar"][data-state="collapsed"] {
    width: 280px !important;
  }

  [data-sidebar="sidebar"][data-state="collapsed"] span {
    display: inline-block !important;
    opacity: 1 !important;
    visibility: visible !important;
  }

  /* Override any group-data collapsed styles */
  [data-sidebar="sidebar"] .group-data-\[state\=collapsed\]\:hidden,
  [data-sidebar="sidebar"] [class*="group-data-[state=collapsed]:hidden"] {
    display: inline-block !important;
    opacity: 1 !important;
    visibility: visible !important;
  }

  /* Force menu buttons to show text */
  [data-sidebar="sidebar"] button span,
  [data-sidebar="sidebar"] a span {
    display: inline-block !important;
    margin-left: 8px !important;
    opacity: 1 !important;
    visibility: visible !important;
    font-size: 14px !important;
  }

  /* Mobile sidebar content spacing */
  [data-sidebar="sidebar"][data-mobile="true"] {
    padding: 0 !important;
  }

  [data-sidebar="sidebar"][data-mobile="true"] [data-slot="sidebar-content"] {
    padding: 16px 0 !important;
  }

  [data-sidebar="sidebar"][data-mobile="true"] [data-slot="sidebar-group"] {
    margin-bottom: 24px !important;
  }

  [data-sidebar="sidebar"][data-mobile="true"]
    [data-slot="sidebar-group-label"] {
    padding: 8px 16px !important;
    font-size: 14px !important;
    font-weight: 600 !important;
    color: var(--sidebar-foreground) !important;
    opacity: 0.7 !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
  }

  /* Mobile navigation items */
  [data-sidebar="sidebar"][data-mobile="true"]
    [data-slot="sidebar-menu-button"] {
    margin: 2px 12px !important;
    padding: 12px 16px !important;
    border-radius: 8px !important;
    font-size: 16px !important;
    font-weight: 500 !important;
    min-height: 48px !important;
    transition: all 0.2s ease !important;
  }

  [data-sidebar="sidebar"][data-mobile="true"]
    [data-slot="sidebar-menu-button"]:hover {
    background-color: var(--sidebar-accent) !important;
    color: var(--sidebar-accent-foreground) !important;
  }

  /* Mobile sidebar footer */
  [data-sidebar="sidebar"][data-mobile="true"] [data-slot="sidebar-footer"] {
    padding: 16px !important;
    border-top: 1px solid var(--sidebar-border) !important;
    margin-top: auto !important;
  }
}

/* Desktop sidebar adjustments */
@media (min-width: 768px) {
  /* Ensure sidebar trigger is visible on desktop */
  [data-sidebar="trigger"] {
    display: flex !important;
  }

  /* Desktop sidebar styling */
  [data-sidebar="sidebar"] {
    position: relative !important;
    transform: none !important;
  }
}

/* Extra small screens */
@media (max-width: 380px) {
  .mode-toggle-container {
    right: 0.5rem; /* Maintain right positioning */
    top: 0.5rem;
  }

  .mode-toggle {
    height: 1.75rem !important;
    width: 1.75rem !important;
    padding: 0.25rem !important;
  }
}

/* Ensure the toggle button itself stays in position */
.mode-toggle {
  position: relative !important; /* Override any absolute positioning */
  right: 0 !important; /* Ensure it stays right-aligned */
}
