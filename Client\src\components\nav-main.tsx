"use client";

import { ChevronRight, type LucideIcon } from "lucide-react";
import { useEffect, useState } from "react";

import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  useSidebar,
} from "@/components/ui/sidebar";

export function NavMain({
  items,
}: {
  items: {
    title: string;
    url: string;
    icon?: LucideIcon;
    isActive?: boolean;
    items?: {
      title: string;
      url: string;
    }[];
  }[];
}) {
  const { isMobile } = useSidebar();

  return (
    <SidebarGroup className={isMobile ? "px-0" : ""}>
      <SidebarGroupLabel
        className={
          isMobile
            ? "px-4 py-3 text-xs font-semibold text-sidebar-foreground/60 uppercase tracking-wider"
            : ""
        }
      >
        Navigation
      </SidebarGroupLabel>
      <SidebarMenu className={isMobile ? "space-y-1 px-3" : ""}>
        {items.map((item) => {
          // If item has sub-items, render as collapsible
          if (item.items && item.items.length > 0) {
            return (
              <Collapsible
                key={item.title}
                asChild
                defaultOpen={item.isActive}
                className="group/collapsible"
              >
                <SidebarMenuItem className={isMobile ? "w-full" : ""}>
                  <CollapsibleTrigger asChild>
                    <SidebarMenuButton
                      tooltip={!isMobile ? item.title : undefined}
                      className={
                        isMobile
                          ? "w-full h-12 px-3 justify-start text-left hover:bg-sidebar-accent hover:text-sidebar-accent-foreground"
                          : ""
                      }
                    >
                      {item.icon && (
                        <item.icon
                          className={isMobile ? "h-5 w-5 flex-shrink-0" : ""}
                        />
                      )}
                      <span
                        className={
                          isMobile ? "flex-1 text-sidebar-foreground" : ""
                        }
                      >
                        {item.title}
                      </span>
                      <ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
                    </SidebarMenuButton>
                  </CollapsibleTrigger>
                  <CollapsibleContent>
                    <SidebarMenuSub>
                      {item.items.map((subItem) => (
                        <SidebarMenuSubItem key={subItem.title}>
                          <SidebarMenuSubButton asChild>
                            <a href={subItem.url}>{subItem.title}</a>
                          </SidebarMenuSubButton>
                        </SidebarMenuSubItem>
                      ))}
                    </SidebarMenuSub>
                  </CollapsibleContent>
                </SidebarMenuItem>
              </Collapsible>
            );
          }

          // If item has no sub-items, render as simple link
          return (
            <SidebarMenuItem
              key={item.title}
              className={isMobile ? "w-full" : ""}
            >
              <SidebarMenuButton
                asChild
                tooltip={!isMobile ? item.title : undefined}
                className={
                  isMobile
                    ? "w-full h-12 px-3 justify-start text-left hover:bg-sidebar-accent hover:text-sidebar-accent-foreground"
                    : ""
                }
              >
                <a
                  href={item.url}
                  className={isMobile ? "flex items-center gap-3 w-full" : ""}
                >
                  {item.icon && (
                    <item.icon
                      className={isMobile ? "h-5 w-5 flex-shrink-0" : ""}
                    />
                  )}
                  <span
                    className={isMobile ? "flex-1 text-sidebar-foreground" : ""}
                  >
                    {item.title}
                  </span>
                </a>
              </SidebarMenuButton>
            </SidebarMenuItem>
          );
        })}
      </SidebarMenu>
    </SidebarGroup>
  );
}
