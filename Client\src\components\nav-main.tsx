"use client";

import { ChevronRight, type LucideIcon } from "lucide-react";
import { useEffect, useState } from "react";

import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  useSidebar,
} from "@/components/ui/sidebar";

export function NavMain({
  items,
}: {
  items: {
    title: string;
    url: string;
    icon?: LucideIcon;
    isActive?: boolean;
    items?: {
      title: string;
      url: string;
    }[];
  }[];
}) {
  const { isMobile, state } = useSidebar();

  return (
    <SidebarGroup className={isMobile ? "px-2" : ""}>
      <SidebarGroupLabel
        className={
          isMobile
            ? "px-2 py-3 text-sm font-medium text-sidebar-foreground/70"
            : ""
        }
      >
        Navigation
      </SidebarGroupLabel>
      <SidebarMenu className={isMobile ? "space-y-1" : ""}>
        {items.map((item) => (
          <SidebarMenuItem key={item.title}>
            <SidebarMenuButton
              asChild
              tooltip={!isMobile ? item.title : undefined}
              className={
                isMobile
                  ? "h-12 px-3 text-base font-medium hover:bg-sidebar-accent hover:text-sidebar-accent-foreground"
                  : ""
              }
            >
              <a
                href={item.url}
                className={isMobile ? "flex items-center gap-3" : ""}
              >
                {item.icon && (
                  <item.icon className={isMobile ? "h-5 w-5" : ""} />
                )}
                <span className={isMobile ? "text-sidebar-foreground" : ""}>
                  {item.title}
                </span>
              </a>
            </SidebarMenuButton>
          </SidebarMenuItem>
        ))}
      </SidebarMenu>
    </SidebarGroup>
  );
}
